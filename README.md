# OverTheWire Wargames Client Script

A Python automation toolkit for **OverTheWire wargames** - streamlining SSH connections, password management, and challenge documentation for cybersecurity learning.

## 🎯 Overview

This project provides two main tools to enhance your OverTheWire wargaming experience:
- **`wargames.py`**: Automated SSH connection manager with password handling
- **`report.py`**: Challenge description scraper and writeup template generator

## 🚀 Quick Start

### Prerequisites
- Python 3.6 or newer
- SSH client
- pip3

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd OverTheWireWargames

# Install dependencies
pip3 install -r requirements.txt
```

### Basic Usage
```bash
# Connect to bandit level 5
python wargames.py -g bandit -n 5

# Generate writeup templates for all bandit levels
python report.py -g bandit
```

## 🔧 Tools

### wargames.py - SSH Connection Manager

Automates SSH connections to OverTheWire wargame levels with intelligent password management.

#### Features
- **Automatic SSH setup**: Handles correct ports and addresses for each game
- **Password management**: Saves/loads passwords automatically with clipboard integration
- **Level cycling**: Progress through consecutive levels seamlessly
- **Cross-platform**: Works on Windows, Linux, and macOS

#### Supported Games
| Game | Port | Levels |
|------|------|--------|
| bandit | 2220 | 34 |
| krypton | 2231 | 8 |
| leviathan | 2223 | 8 |
| natas | Web-based | 34 |
| narnia | 2226 | 9 |
| behemoth | 2221 | 8 |
| utumno | 2227 | 8 |
| maze | 2225 | 9 |
| vortex | - | 27 |
| manpage | 2224 | 7 |

#### Command Line Options
```bash
-g, --game      Game name (required) - choose from supported games above
-n, --number    Connect to specific level number
-c, --cycle     Start from level and cycle through remaining levels
-p, --password  Enable password management (optional)
```

#### Usage Examples
```bash
# Connect to bandit level 5
python wargames.py -g bandit -n 5

# Cycle through krypton starting from level 3 with password management
python wargames.py -g krypton -c 3 -p

# Resume from last saved password
python wargames.py -g bandit -p

# Connect to specific level without password management
python wargames.py -g leviathan -n 2
```

### report.py - Challenge Documentation Generator

Scrapes challenge descriptions from OverTheWire website and creates structured writeup templates.

#### Features
- **Web scraping**: Extracts challenge descriptions directly from OverTheWire
- **Template generation**: Creates organized writeup templates with sections for steps and passwords
- **Flexible output**: Generate templates for individual levels or entire games
- **Auto-naming**: Prevents file conflicts with automatic numbering

#### Command Line Options
```bash
-g, --game      Game name (required) - same games as wargames.py
-f, --file      Output filename (optional) - defaults to {game}-steps.txt
-n, --number    Specific level to scrape (optional) - scrapes all levels if not specified
```

#### Usage Examples
```bash
# Generate writeup template for all bandit levels
python report.py -g bandit

# Generate template for specific level with custom filename
python report.py -g krypton -n 5 -f my-krypton-notes.txt

# Generate template for all levels of a specific game
python report.py -g leviathan -f leviathan-writeups.txt
```

#### Output Format
Generated files include structured sections for each level:
```text
-------
steps:


-------

----------
password:
----------
```

## 🛠️ Dependencies

Install all dependencies with:
```bash
pip3 install -r requirements.txt
```

Or install individually:
```bash
pip3 install beautifulsoup4 requests pyperclip
```

## 🎯 Use Cases

This toolkit is perfect for:
- **Cybersecurity students** learning through OverTheWire challenges
- **CTF enthusiasts** who want streamlined workflow management
- **Penetration testers** practicing skills on wargame platforms
- **Security researchers** documenting their learning journey

## 🤝 Contributing

Contributions are welcome! Feel free to submit issues, feature requests, or pull requests.

## 📝 License

This project is open source. Please check the license file for details.

## 🔗 Links

- [OverTheWire Wargames](https://overthewire.org/wargames/)
- [Project Repository](https://github.com/nikolasfil/OverTheWireWargames)
