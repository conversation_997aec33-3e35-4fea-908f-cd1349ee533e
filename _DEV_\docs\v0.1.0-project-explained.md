# OverTheWire Wargames Client Script

This is a Python automation toolkit for **OverTheWire wargames** - a popular cybersecurity learning platform that hosts various hacking challenges and CTF (Capture The Flag) games.

## Project Components:

### 1. Main Script (`wargames.py`)
- Automates SSH connections to different OverTheWire wargame levels
- Supports all major OverTheWire games: bandit, krypton, leviathan, natas, narnia, behemoth, utumno, maze, vortex, and manpage
- Features password management with automatic saving/loading and clipboard integration
- Provides cycling through levels or connecting to specific levels
- Handles the correct SSH ports and addresses for each game automatically

### 2. Report Generator (`report.py`)
- Web scraper that extracts challenge descriptions from the OverTheWire website
- Creates structured writeup templates for documenting solutions
- Generates files with sections for steps and passwords for each level
- Supports scraping individual levels or entire games

## Key Features:

- **Cross-platform compatibility** (Windows, Linux, macOS)
- **Password management**: Automatically saves and retrieves passwords between levels
- **Clipboard integration**: Copies passwords to clipboard for easy pasting
- **Level cycling**: Can automatically progress through consecutive levels
- **Challenge documentation**: Creates templates for writeups and solution tracking

## Use Cases:

This tool is designed for cybersecurity students, penetration testers, and CTF enthusiasts who want to:
- Streamline their workflow when solving OverTheWire challenges
- Automatically manage SSH connections and passwords
- Document their solutions in an organized manner
- Focus on solving challenges rather than managing connection details

## Summary

The project essentially removes the tedious manual work of connecting to different levels and managing credentials, allowing users to focus on the actual security challenges and learning.

## Technical Details

### Supported Games and Ports:
- **leviathan**: Port 2223
- **krypton**: Port 2231
- **bandit**: Port 2220
- **narnia**: Port 2226
- **behemoth**: Port 2221
- **utumno**: Port 2227
- **maze**: Port 2225
- **manpage**: Port 2224
- **natas**: Web-based (Port 0)

### Dependencies:
- Python 3.6 or newer
- SSH client
- beautifulsoup4 (for report.py)
- requests (for report.py)
- pyperclip (for clipboard functionality)

### Usage Examples:

#### wargames.py:
```bash
# Connect to a specific level
python wargames.py -g bandit -n 5

# Cycle through levels starting from level 3
python wargames.py -g bandit -c 3 -p

# Resume from last saved password
python wargames.py -g bandit -p
```

#### report.py:
```bash
# Generate writeup template for all levels
python report.py -g bandit

# Generate template for specific level
python report.py -g bandit -n 5 -f my-bandit-notes.txt
```
