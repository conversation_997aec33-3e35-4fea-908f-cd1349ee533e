{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://example.com/games-schema.json", "title": "OverTheWire Games Configuration Schema", "description": "Schema for validating OverTheWire wargames configuration", "type": "object", "properties": {"games": {"type": "object", "description": "Collection of wargame configurations", "patternProperties": {"^[a-z][a-z0-9]*$": {"type": "object", "description": "Individual game configuration", "properties": {"port": {"type": "integer", "description": "SSH port number for the game (0 for web-based games)", "minimum": 0, "maximum": 65535}, "levels": {"type": "integer", "description": "Number of levels in the game", "minimum": 1, "maximum": 100}, "description": {"type": "string", "description": "Brief description of the game", "minLength": 10, "maxLength": 200}}, "required": ["port", "levels"], "additionalProperties": false}}, "additionalProperties": false, "minProperties": 1}}, "required": ["games"], "additionalProperties": false}