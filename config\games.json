{"games": {"leviathan": {"port": 2223, "levels": 8, "description": "Leviathan wargame - A collection of small programs to teach basic exploitation"}, "krypton": {"port": 2231, "levels": 8, "description": "Krypton wargame - A series of levels teaching cryptography"}, "natas": {"port": 0, "levels": 34, "description": "Natas wargame - Web application security challenges"}, "bandit": {"port": 2220, "levels": 34, "description": "Bandit wargame - Basic Linux commands and security concepts"}, "narnia": {"port": 2226, "levels": 9, "description": "Narnia wargame - Basic exploitation challenges"}, "behemoth": {"port": 2221, "levels": 8, "description": "Behemoth wargame - Binary exploitation challenges"}, "utumno": {"port": 2227, "levels": 8, "description": "Utumno wargame - Advanced binary exploitation"}, "maze": {"port": 2225, "levels": 9, "description": "Maze wargame - Various security challenges"}, "manpage": {"port": 2224, "levels": 7, "description": "Manpage wargame - Manual page reading challenges"}, "vortex": {"port": 0, "levels": 27, "description": "Vortex wargame - Advanced exploitation challenges"}}}