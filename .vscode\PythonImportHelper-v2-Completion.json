[{"label": "runpy", "kind": 6, "isExtraImport": true, "importPath": "runpy", "description": "runpy", "detail": "runpy", "documentation": {}}, {"label": "annotations", "importPath": "__future__", "description": "__future__", "isExtraImport": true, "detail": "__future__", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "BeautifulSoup", "importPath": "bs4", "description": "bs4", "isExtraImport": true, "detail": "bs4", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "platform", "kind": 6, "isExtraImport": true, "importPath": "platform", "description": "platform", "detail": "platform", "documentation": {}}, {"label": "pyperclip", "kind": 6, "isExtraImport": true, "importPath": "pyperclip", "description": "pyperclip", "detail": "pyperclip", "documentation": {}}, {"label": "argcomplete", "kind": 6, "isExtraImport": true, "importPath": "argcomplete", "description": "argcomplete", "detail": "argcomplete", "documentation": {}}, {"label": "bin_dir", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "bin_dir = os.path.dirname(abs_file)\nbase = bin_dir[: -len(\"Scripts\") - 1]  # strip away the bin part from the __file__, plus the path separator\n# prepend bin to PATH (this file is inside the bin directory)\nos.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"OverTheWireWargames\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "base", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "base = bin_dir[: -len(\"Scripts\") - 1]  # strip away the bin part from the __file__, plus the path separator\n# prepend bin to PATH (this file is inside the bin directory)\nos.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"OverTheWireWargames\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"PATH\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"OverTheWireWargames\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"VIRTUAL_ENV\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"OverTheWireWargames\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"VIRTUAL_ENV_PROMPT\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"VIRTUAL_ENV_PROMPT\"] = \"OverTheWireWargames\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "prev_length", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "prev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.path[:]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.real_prefix", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.prefix", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "file_existing", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def file_existing(file):\n    ls = os.listdir()\n    if '.' in file:\n        f = file.split('.')\n        if file in ls:\n            i=1\n            while True:\n                file = \".\".join([f\"{f[0]}_{i}\",f[1]]) \n                if file not in ls:\n                    return file", "detail": "report", "documentation": {}}, {"label": "clear", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def clear():\n    #yes there is a more efficient way \n    #to discern os\n    try:\n        os.system('clc')\n    except:\n        pass \n    try :\n        os.system('clear')\n    except : ", "detail": "report", "documentation": {}}, {"label": "url", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def url(num,game):\n    return f\"https://overthewire.org/wargames/{game}/{game}{num}.html\"\ndef scraping(num,game):\n    header = {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'}\n    request = requests.get(url(num,game),headers=header)\n    soup = BeautifulSoup(request.text, 'html.parser')    \n    text = soup.find('div',id=\"content\")\n    if text is not None:\n        return text.get_text()\ndef scraping_general(game):", "detail": "report", "documentation": {}}, {"label": "scraping", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def scraping(num,game):\n    header = {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'}\n    request = requests.get(url(num,game),headers=header)\n    soup = BeautifulSoup(request.text, 'html.parser')    \n    text = soup.find('div',id=\"content\")\n    if text is not None:\n        return text.get_text()\ndef scraping_general(game):\n    header = {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'}\n    request = requests.get(f\"https://overthewire.org/wargames/{game}\",headers=header)", "detail": "report", "documentation": {}}, {"label": "scraping_general", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def scraping_general(game):\n    header = {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'}\n    request = requests.get(f\"https://overthewire.org/wargames/{game}\",headers=header)\n    soup = BeautifulSoup(request.text, 'html.parser')    \n    text = soup.find('div',id=\"content\")\n    return text.get_text()\ndef writing(f,i,game):\n    f.write(f\"\\n{'-'*2*len('Level 1 → Level 1')}\\n\\nLevel {i} → Level {i+1}\\n\\n{'-'*2*len('Level 15 → Level 16')}\\n\")\n    # f.write(f\"{scraping(i+1,game)}\\n\")\n    f.write(f\"{scraping(i,game)}\\n\")", "detail": "report", "documentation": {}}, {"label": "writing", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def writing(f,i,game):\n    f.write(f\"\\n{'-'*2*len('Level 1 → Level 1')}\\n\\nLevel {i} → Level {i+1}\\n\\n{'-'*2*len('Level 15 → Level 16')}\\n\")\n    # f.write(f\"{scraping(i+1,game)}\\n\")\n    f.write(f\"{scraping(i,game)}\\n\")\n    f.write(f'\\n{\"-\"*len(\"steps: \")}\\nsteps: \\n\\n\\n{\"-\"*len(\"steps: \")}\\n\\n')\n    f.write(f'\\n{\"-\"*len(\"password: \")}\\npassword: \\n{\"-\"*len(\"password: \")}\\n')\ndef main():\n    #general command to know \n    h1='\\nSSH wargames writeup with challenge\\'s description'", "detail": "report", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "report", "description": "report", "peekOfCode": "def main():\n    #general command to know \n    h1='\\nSSH wargames writeup with challenge\\'s description'\n    h2='\\nWith -f/--file specify the name of the report (optional, there is a default value of {game}-steps.txt\\nWith -n/--number you can specify a level to scrape \\n'\n    h3='\\nWith -g/--game you can specify the game to construct the report of [leviathan,krypton,bandit,natas,narnia,behemoth,utumno,maze,vortex,manpage]\\n'\n    #Parser Arguments\n    parser = argparse.ArgumentParser(description=f\"{h1}\\n{h2}\\n{h3}\")\n    parser.add_argument('--file', '-f', type=str, help='File Name')\n    parser.add_argument('--number', '-n', type=int, help='Scrapes Only one Level')\n    parser.add_argument('--game', '-g', type=str, help='Specifies the wargame')", "detail": "report", "documentation": {}}, {"label": "WarGames", "kind": 6, "importPath": "wargames", "description": "wargames", "peekOfCode": "class WarGames:\n    def __init__(self):\n        self.wargames = {\n            \"leviathan\": 2223,\n            \"krypton\": 2231,\n            \"natas\": 0,\n            \"bandit\": 2220,\n            \"narnia\": 2226,\n            \"behemoth\": 2221,\n            \"utumno\": 2227,", "detail": "wargames", "documentation": {}}]