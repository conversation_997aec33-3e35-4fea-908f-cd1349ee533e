#!/bin/python
import argparse
import json
import os
import sys

import requests
from bs4 import BeautifulSoup

#This is a script to create writeups of overthewire wargames  
#https://overthewire.org/wargames/bandit
#It works on both windows and linux systems 

#Author :  <PERSON><PERSON><PERSON><PERSON> 

def load_game_config():
    """
    Load game configurations from the JSON config file.
    
    Returns
    -------
    dict
        Dictionary containing game configurations with levels and other metadata.
        
    Raises
    ------
    FileNotFoundError
        If the configuration file is not found.
    json.JSONDecodeError
        If the configuration file contains invalid JSON.
    KeyError
        If the configuration file doesn't have the expected structure.
    """
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'games.json')
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Extract levels dictionary from the config
        if 'games' not in config:
            raise KeyError("Configuration file missing 'games' key")
            
        levels = {}
        for game_name, game_data in config['games'].items():
            if 'levels' not in game_data:
                raise KeyError(f"Game '{game_name}' missing 'levels' key")
            levels[game_name] = game_data['levels']
            
        return levels
        
    except FileNotFoundError:
        print(f"Error: Configuration file not found at {config_path}")
        print("Please ensure the config/games.json file exists.")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in configuration file: {e}")
        sys.exit(1)
    except KeyError as e:
        print(f"Error: Configuration file structure error: {e}")
        sys.exit(1)

def file_existing(file):
    """
    Returns a filename that does not exist in the current directory.
    
    If a file with the given name does not exist, it is returned.
    If a file with the given name exists, a new name is generated by
    appending a number to the end of the filename. This number is
    incremented until a filename is found that does not exist.
    
    Parameters
    ----------
    file : str
        The name of the file to check.
    
    Returns
    -------
    str
        The name of a file that does not exist.
    """
    
    # Check in the project root directory (parent of src)
    project_root = os.path.join(os.path.dirname(__file__), '..')
    ls = os.listdir(project_root)
    
    if '.' in file:
        f = file.split('.')
        if file in ls:
            i=1
            while True:
                file = ".".join([f"{f[0]}_{i}",f[1]]) 
                if file not in ls:
                    return file
                i+=1        
    else:
        if file in ls:
            i=1
            while True:
                file = f"{file}_{i}" 
                if file not in ls:
                    return file
                i+=1
    return file

        


def clear():
    #yes there is a more efficient way 
    #to discern os
    """
    Clears the console screen in a platform independent way.

    This function is used to clear the console screen in a platform
    independent way. If the platform is windows, 'clc' is used.
    If the platform is not windows, 'clear' is used.

    Returns
    -------
    None
    """
    try:
        os.system('clc')
    except:
        pass 

    try :
        os.system('clear')
    except : 
        pass  



def url(num,game):
    """
    Returns the URL for a given level of a given game.
    
    Parameters
    ----------
    num : int
        The level number.
    game : str
        The name of the game.
    
    Returns
    -------
    str
        The URL for the given level of the given game.
    """
    return f"https://overthewire.org/wargames/{game}/{game}{num}.html"


def scraping(num,game):

    """
    Scrapes the description of a given level of a given game from the
    overthewire.org website.

    Parameters
    ----------
    num : int
        The level number.
    game : str
        The name of the game.

    Returns
    -------
    str
        The description of the given level of the given game.
    """
    header = {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'}
    request = requests.get(url(num,game),headers=header)
    soup = BeautifulSoup(request.text, 'html.parser')    
    text = soup.find('div',id="content")
    if text is not None:
        return text.get_text()


def scraping_general(game):

    header = {'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'}
    request = requests.get(f"https://overthewire.org/wargames/{game}",headers=header)
    soup = BeautifulSoup(request.text, 'html.parser')    
    text = soup.find('div',id="content")
    return text.get_text()



def writing(f,i,game):
    
    f.write(f"\n{'-'*2*len('Level 1 → Level 1')}\n\nLevel {i} → Level {i+1}\n\n{'-'*2*len('Level 15 → Level 16')}\n")
    
    # f.write(f"{scraping(i+1,game)}\n")
    f.write(f"{scraping(i,game)}\n")

    f.write(f'\n{"-"*len("steps: ")}\nsteps: \n\n\n{"-"*len("steps: ")}\n\n')

    f.write(f'\n{"-"*len("password: ")}\npassword: \n{"-"*len("password: ")}\n')
            





def main():
    #general command to know 
    
    h1='\nSSH wargames writeup with challenge\'s description'

    h2='\nWith -f/--file specify the name of the report (optional, there is a default value of {game}-steps.txt\nWith -n/--number you can specify a level to scrape \n'
    h3='\nWith -g/--game you can specify the game to construct the report of [leviathan,krypton,bandit,natas,narnia,behemoth,utumno,maze,vortex,manpage]\n'

    
    #Parser Arguments
    parser = argparse.ArgumentParser(description=f"{h1}\n{h2}\n{h3}")
    parser.add_argument('--file', '-f', type=str, help='File Name')
    parser.add_argument('--number', '-n', type=int, help='Scrapes Only one Level')
    parser.add_argument('--game', '-g', type=str, help='Specifies the wargame')

    args = parser.parse_args()
    
    if args.game is not None:

        # Load game configurations from JSON file
        levels = load_game_config()
        
        # Validate that the specified game exists in the configuration
        if args.game not in levels:
            available_games = ', '.join(sorted(levels.keys()))
            print(f"Error: Game '{args.game}' not found in configuration.")
            print(f"Available games: {available_games}")
            sys.exit(1)
            
        
        if args.file is None:
            # args.file = f'{args.game}-steps.txt'
            args.file = file_existing(f'{args.game}-steps.txt')

        # Ensure output file is created in the project root directory
        project_root = os.path.join(os.path.dirname(__file__), '..')
        output_file_path = os.path.join(project_root, args.file)

        #Main excecution
        if args.number is None :  
            with open(output_file_path,'w') as f:
                clear()
                progress='Progress:[='
                f.write(f"\n{'-'*10}\n{args.game.upper()}\n{'-'*10}\n")
                progress+='='
                print(progress)
                f.write(f"{scraping_general(args.game)}\n")  
                for i in range(levels[args.game]):
                    progress+='='
                    writing(f,i,args.game)
                    clear()
                    print(progress)
                progress+=']'
                clear()
                print(progress)
            

        else:
            with open(output_file_path,'w') as f:
                f.write(f"\n{'-'*10}\n{args.game.upper()}\n{'-'*10}\n")
                f.write(f"{scraping_general(args.game)}\n")  
                writing(f,args.number,args.game)
    
    else:
        # Load game configurations to show available games
        try:
            levels = load_game_config()
            available_games = ', '.join(sorted(levels.keys()))
            h3_updated = f'\nWith -g/--game you can specify the game to construct the report of [{available_games}]\n'
            print(h3_updated)
        except SystemExit:
            # If config loading fails, show the original help message
            print(h3)


if __name__=='__main__':
    main()